using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LessonsController : ControllerBase
    {
        private readonly ILessonService _lessonService;
        private readonly ILogger<LessonsController> _logger;

        public LessonsController(ILessonService lessonService, ILogger<LessonsController> logger)
        {
            _lessonService = lessonService;
            _logger = logger;
        }

        // GET: api/lessons
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ScheduleEvent>>> GetLessons()
        {
            try
            {
                var lessons = await _lessonService.GetLessonsAsync();
                return Ok(lessons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lessons");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/lessons/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ScheduleEvent>> GetLesson(int id)
        {
            try
            {
                var lesson = await _lessonService.GetLessonAsync(id);
                if (lesson == null)
                {
                    return NotFound();
                }
                return Ok(lesson);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lesson {LessonId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/lessons
        [HttpPost]
        public async Task<ActionResult<ScheduleEvent>> CreateLesson(ScheduleEvent scheduleEvent)
        {
            try
            {
                var result = await _lessonService.CreateLessonAsync(scheduleEvent);
                return CreatedAtAction(nameof(GetLesson), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating lesson");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/lessons/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLesson(int id, ScheduleEvent scheduleEvent)
        {
            if (id != scheduleEvent.Id)
            {
                return BadRequest();
            }

            try
            {
                var success = await _lessonService.UpdateLessonAsync(id, scheduleEvent);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lesson {LessonId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/lessons/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLesson(int id)
        {
            try
            {
                var success = await _lessonService.DeleteLessonAsync(id);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting lesson {LessonId}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
