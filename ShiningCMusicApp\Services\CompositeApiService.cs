using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    /// <summary>
    /// Composite service that provides access to all API services for backward compatibility
    /// </summary>
    public interface ICompositeApiService
    {
        // Lesson operations
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);

        // Tutor operations
        Task<List<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor?> CreateTutorAsync(<PERSON><PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tu<PERSON> tutor);
        Task<bool> DeleteTutorAsync(int id);
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);

        // Student operations
        Task<List<Student>> GetStudentsAsync();
        Task<Student?> GetStudentAsync(int id);
        Task<Student?> CreateStudentAsync(Student student);
        Task<bool> UpdateStudentAsync(int id, Student student);
        Task<bool> DeleteStudentAsync(int id);

        // Subject operations
        Task<List<Subject>> GetSubjectsAsync();
        Task<Subject?> GetSubjectAsync(int id);
        Task<Subject?> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(int id, Subject subject);
        Task<bool> DeleteSubjectAsync(int id);

        // Location operations
        Task<List<Location>> GetLocationsAsync();
        Task<Location?> GetLocationAsync(int id);
        Task<Location?> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(int id, Location location);
        Task<bool> DeleteLocationAsync(int id);

        // User operations
        Task<User?> AuthenticateAsync(string loginName, string password);
        Task<List<User>> GetUsersAsync();
        Task<List<UserRole>> GetUserRolesAsync();
        Task<User?> GetUserAsync(string loginName);
        Task<User?> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(string loginName, User user);
        Task<bool> DeleteUserAsync(string loginName);
        Task<UserRole?> CreateUserRoleAsync(UserRole role);
        Task<bool> UpdateUserRoleAsync(UserRole role);
        Task<bool> DeleteUserRoleAsync(int roleId);
    }

    public class CompositeApiService : ICompositeApiService
    {
        private readonly ILessonApiService _lessonService;
        private readonly ITutorApiService _tutorService;
        private readonly IStudentApiService _studentService;
        private readonly ISubjectApiService _subjectService;
        private readonly ILocationApiService _locationService;
        private readonly IUserApiService _userService;

        public CompositeApiService(
            ILessonApiService lessonService,
            ITutorApiService tutorService,
            IStudentApiService studentService,
            ISubjectApiService subjectService,
            ILocationApiService locationService,
            IUserApiService userService)
        {
            _lessonService = lessonService;
            _tutorService = tutorService;
            _studentService = studentService;
            _subjectService = subjectService;
            _locationService = locationService;
            _userService = userService;
        }

        // Lesson operations
        public Task<List<ScheduleEvent>> GetLessonsAsync() => _lessonService.GetLessonsAsync();
        public Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson) => _lessonService.CreateLessonAsync(lesson);
        public Task<bool> UpdateLessonAsync(ScheduleEvent lesson) => _lessonService.UpdateLessonAsync(lesson);
        public Task<bool> DeleteLessonAsync(int id) => _lessonService.DeleteLessonAsync(id);

        // Tutor operations
        public Task<List<Tutor>> GetTutorsAsync() => _tutorService.GetTutorsAsync();
        public Task<Tutor?> GetTutorAsync(int id) => _tutorService.GetTutorAsync(id);
        public Task<Tutor?> CreateTutorAsync(Tutor tutor) => _tutorService.CreateTutorAsync(tutor);
        public Task<bool> UpdateTutorAsync(int id, Tutor tutor) => _tutorService.UpdateTutorAsync(id, tutor);
        public Task<bool> DeleteTutorAsync(int id) => _tutorService.DeleteTutorAsync(id);
        public Task<bool> UpdateTutorColorAsync(int tutorId, string color) => _tutorService.UpdateTutorColorAsync(tutorId, color);

        // Student operations
        public Task<List<Student>> GetStudentsAsync() => _studentService.GetStudentsAsync();
        public Task<Student?> GetStudentAsync(int id) => _studentService.GetStudentAsync(id);
        public Task<Student?> CreateStudentAsync(Student student) => _studentService.CreateStudentAsync(student);
        public Task<bool> UpdateStudentAsync(int id, Student student) => _studentService.UpdateStudentAsync(id, student);
        public Task<bool> DeleteStudentAsync(int id) => _studentService.DeleteStudentAsync(id);

        // Subject operations
        public Task<List<Subject>> GetSubjectsAsync() => _subjectService.GetSubjectsAsync();
        public Task<Subject?> GetSubjectAsync(int id) => _subjectService.GetSubjectAsync(id);
        public Task<Subject?> CreateSubjectAsync(Subject subject) => _subjectService.CreateSubjectAsync(subject);
        public Task<bool> UpdateSubjectAsync(int id, Subject subject) => _subjectService.UpdateSubjectAsync(id, subject);
        public Task<bool> DeleteSubjectAsync(int id) => _subjectService.DeleteSubjectAsync(id);

        // Location operations
        public Task<List<Location>> GetLocationsAsync() => _locationService.GetLocationsAsync();
        public Task<Location?> GetLocationAsync(int id) => _locationService.GetLocationAsync(id);
        public Task<Location?> CreateLocationAsync(Location location) => _locationService.CreateLocationAsync(location);
        public Task<bool> UpdateLocationAsync(int id, Location location) => _locationService.UpdateLocationAsync(id, location);
        public Task<bool> DeleteLocationAsync(int id) => _locationService.DeleteLocationAsync(id);

        // User operations
        public Task<User?> AuthenticateAsync(string loginName, string password) => _userService.AuthenticateAsync(loginName, password);
        public Task<List<User>> GetUsersAsync() => _userService.GetUsersAsync();
        public Task<List<UserRole>> GetUserRolesAsync() => _userService.GetUserRolesAsync();
        public Task<User?> GetUserAsync(string loginName) => _userService.GetUserAsync(loginName);
        public Task<User?> CreateUserAsync(User user) => _userService.CreateUserAsync(user);
        public Task<bool> UpdateUserAsync(string loginName, User user) => _userService.UpdateUserAsync(loginName, user);
        public Task<bool> DeleteUserAsync(string loginName) => _userService.DeleteUserAsync(loginName);
        public Task<UserRole?> CreateUserRoleAsync(UserRole role) => _userService.CreateUserRoleAsync(role);
        public Task<bool> UpdateUserRoleAsync(UserRole role) => _userService.UpdateUserRoleAsync(role);
        public Task<bool> DeleteUserRoleAsync(int roleId) => _userService.DeleteUserRoleAsync(roleId);
    }
}
