USE [Shining]
GO

-- Insert sample tutors
INSERT INTO [dbo].[Tu<PERSON>] ([<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON><PERSON>ame], [Password], [Color])
VALUES 
('<PERSON>', '<EMAIL>', 'sue', 'password123', '#FF6B6B'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'koeun', 'password123', '#4ECDC4'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'kathrin', 'password123', '#7e57c2'),
('<PERSON>fellow', '<EMAIL>', 'mitchell', 'password123', '#1e88e5')
GO

-- Insert sample students
INSERT INTO [dbo].[Students] ([StudentName], [Email], [TutorID])
VALUES 
('<PERSON>', '<EMAIL>', 1),
('<PERSON>', '<EMAIL>', 1),
('<PERSON>', '<EMAIL>', 2),
('<PERSON>', '<EMAIL>', 2),
('<PERSON>', '<EMAIL>', 3)
<PERSON>

-- Set default colors for existing tutors
UPDATE [dbo].[Tutors] SET Color = '#FF6B6B' WHERE TutorId = 1;
UPDATE [dbo].[Tutors] SET Color = '#4ECDC4' WHERE TutorId = 2;