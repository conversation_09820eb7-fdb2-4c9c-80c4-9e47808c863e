USE [MusicSchool]
GO

-- Insert sample tutors
INSERT INTO [dbo].[Tu<PERSON>] ([<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON>ginName], [Password], [Color])
VALUES 
('<PERSON>', '<EMAIL>', 'sue', 'password123', '#FF6B6B'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'koeun', 'password123', '#4ECDC4'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'kathrin', 'password123', '#5742f5'),
('<PERSON>fellow', '<EMAIL>', 'mitchell', 'password123', '#1e88e5'),
('<PERSON>', '<EMAIL>', 'christy', 'password123', '#ab47bc')
GO

-- Insert sample students
INSERT INTO [dbo].[Students] ([StudentName], [<PERSON><PERSON>], [Instrument], [<PERSON><PERSON><PERSON>])
VALUES 
('<PERSON>', '<EMAIL>', 'Piano', 1),
('<PERSON>', '<EMAIL>', 'Piano', 1),
('<PERSON>', '<EMAIL>', 'Guitar', 2),
('<PERSON> <PERSON>', '<EMAIL>', 'Guitar', 2),
('Ethan <PERSON>', '<EMAIL>', 'Piano', 3)
GO

-- Set default colors for existing tutors
UPDATE [dbo].[Tutors] SET Color = '#FF6B6B' WHERE TutorId = 1;
UPDATE [dbo].[Tutors] SET Color = '#4ECDC4' WHERE TutorId = 2;