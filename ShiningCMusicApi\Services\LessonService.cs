using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services
{
    public interface ILessonService
    {
        Task<IEnumerable<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> GetLessonAsync(int id);
        Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
        Task<IEnumerable<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor> CreateTutorAsync(Tu<PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tutor tutor);
        Task<bool> DeleteTutorAsync(int id);
        Task<IEnumerable<Student>> GetStudentsAsync();
        Task<Student?> GetStudentAsync(int id);
        Task<Student> CreateStudentAsync(Student student);
        Task<bool> UpdateStudentAsync(int id, Student student);
        Task<bool> DeleteStudentAsync(int id);
        Task<IEnumerable<Subject>> GetSubjectsAsync();
        Task<Subject?> GetSubjectAsync(int id);
        Task<Subject> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(int id, Subject subject);
        Task<bool> DeleteSubjectAsync(int id);
        Task<IEnumerable<Location>> GetLocationsAsync();
        Task<Location?> GetLocationAsync(int id);
        Task<Location> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(int id, Location location);
        Task<bool> DeleteLocationAsync(int id);
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
    }

    public class LessonService : ILessonService
    {
        private readonly string _connectionString;

        public LessonService(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;";
        }

        public async Task<IEnumerable<ScheduleEvent>> GetLessonsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    subj.Subject as Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.TutorName,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.IsArchived = 0";

            return await connection.QueryAsync<ScheduleEvent>(sql);
        }

        public async Task<ScheduleEvent?> GetLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    subj.Subject as Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.TutorName,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.LessonId = @Id AND l.IsArchived = 0";

            return await connection.QueryFirstOrDefaultAsync<ScheduleEvent>(sql, new { Id = id });
        }

        public async Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Lessons (SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId, RecurrenceRule, IsRecurring, CreatedUTC)
                VALUES (@SubjectId, @Description, @StartTime, @EndTime, @TutorId, @StudentId, @LocationId, @RecurrenceRule, @IsRecurring, GETUTCDATE());

                SELECT SCOPE_IDENTITY();";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            lesson.Id = newId;
            return lesson;
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Lessons
                SET SubjectId = @SubjectId,
                    Description = @Description,
                    StartTime = @StartTime,
                    EndTime = @EndTime,
                    TutorId = @TutorId,
                    StudentId = @StudentId,
                    LocationId = @LocationId,
                    RecurrenceRule = @RecurrenceRule,
                    IsRecurring = @IsRecurring,
                    UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id AND IsArchived = 0";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                UPDATE Lessons 
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Tutor>> GetTutorsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE IsArchived = 0 ORDER BY TutorName";
            return await connection.QueryAsync<Tutor>(sql);
        }

        public async Task<Tutor?> GetTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE TutorId = @Id AND IsArchived = 0";
            return await connection.QueryFirstOrDefaultAsync<Tutor>(sql, new { Id = id });
        }

        public async Task<Tutor> CreateTutorAsync(Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Tutors (TutorName, Email, LoginName, Password, Color, CreatedUTC, IsArchived)
                VALUES (@TutorName, @Email, @LoginName, @Password, @Color, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                tutor.TutorName,
                tutor.Email,
                tutor.LoginName,
                tutor.Password,
                tutor.Color
            });

            tutor.TutorId = newId;
            return tutor;
        }

        public async Task<bool> UpdateTutorAsync(int id, Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET TutorName = @TutorName,
                    Email = @Email,
                    LoginName = @LoginName,
                    Password = @Password,
                    Color = @Color,
                    UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                tutor.TutorName,
                tutor.Email,
                tutor.LoginName,
                tutor.Password,
                tutor.Color
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Student>> GetStudentsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Students WHERE IsArchived = 0 ORDER BY StudentName";
            return await connection.QueryAsync<Student>(sql);
        }

        public async Task<Student?> GetStudentAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Students WHERE StudentId = @Id AND IsArchived = 0";
            return await connection.QueryFirstOrDefaultAsync<Student>(sql, new { Id = id });
        }

        public async Task<Student> CreateStudentAsync(Student student)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Students (StudentName, Email, TutorID, CreatedUTC, IsArchived)
                VALUES (@StudentName, @Email, @TutorID, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                student.StudentName,
                student.Email,
                student.TutorID
            });

            student.StudentId = newId;
            return student;
        }

        public async Task<bool> UpdateStudentAsync(int id, Student student)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Students
                SET StudentName = @StudentName,
                    Email = @Email,
                    TutorID = @TutorID,
                    UpdatedUTC = GETUTCDATE()
                WHERE StudentId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                student.StudentName,
                student.Email,
                student.TutorID
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Students
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE StudentId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Subject>> GetSubjectsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT SubjectId, Subject as SubjectName FROM Subjects ORDER BY Subject";
            return await connection.QueryAsync<Subject>(sql);
        }

        public async Task<Subject?> GetSubjectAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT SubjectId, Subject as SubjectName FROM Subjects WHERE SubjectId = @Id";
            return await connection.QueryFirstOrDefaultAsync<Subject>(sql, new { Id = id });
        }

        public async Task<Subject> CreateSubjectAsync(Subject subject)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Subjects (Subject)
                VALUES (@SubjectName);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                subject.SubjectName
            });

            subject.SubjectId = newId;
            return subject;
        }

        public async Task<bool> UpdateSubjectAsync(int id, Subject subject)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Subjects
                SET Subject = @SubjectName
                WHERE SubjectId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                subject.SubjectName
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteSubjectAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Subjects
                WHERE SubjectId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Location>> GetLocationsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT LocationId, Location as LocationName FROM Locations ORDER BY Location";
            return await connection.QueryAsync<Location>(sql);
        }

        public async Task<Location?> GetLocationAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT LocationId, Location as LocationName FROM Locations WHERE LocationId = @Id";
            return await connection.QueryFirstOrDefaultAsync<Location>(sql, new { Id = id });
        }

        public async Task<Location> CreateLocationAsync(Location location)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Locations (Location)
                VALUES (@LocationName);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                location.LocationName
            });

            location.LocationId = newId;
            return location;
        }

        public async Task<bool> UpdateLocationAsync(int id, Location location)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Locations
                SET Location = @LocationName
                WHERE LocationId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                location.LocationName
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLocationAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Locations
                WHERE LocationId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateTutorColorAsync(int tutorId, string color)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET Color = @Color, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @TutorId AND IsArchived = 0";

            var parameters = new
            {
                TutorId = tutorId,
                Color = color
            };

            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }
    }
}
