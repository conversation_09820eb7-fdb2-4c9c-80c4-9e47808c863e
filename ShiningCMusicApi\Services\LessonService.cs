using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services
{
    public interface ILessonService
    {
        Task<IEnumerable<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> GetLessonAsync(int id);
        Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
        Task<IEnumerable<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor> CreateTutorAsync(Tu<PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tutor tutor);
        Task<bool> DeleteTutorAsync(int id);
        Task<IEnumerable<Student>> GetStudentsAsync();
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
    }

    public class LessonService : ILessonService
    {
        private readonly string _connectionString;

        public LessonService(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;";
        }

        public async Task<IEnumerable<ScheduleEvent>> GetLessonsAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                SELECT 
                    l.LessonId as Id,
                    l.Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    l.Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    t.TutorName,
                    s.StudentName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                WHERE l.IsArchived = 0";

            return await connection.QueryAsync<ScheduleEvent>(sql);
        }

        public async Task<ScheduleEvent?> GetLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                SELECT 
                    l.LessonId as Id,
                    l.Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    l.Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    t.TutorName,
                    s.StudentName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                WHERE l.LessonId = @Id AND l.IsArchived = 0";

            return await connection.QueryFirstOrDefaultAsync<ScheduleEvent>(sql, new { Id = id });
        }

        public async Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                INSERT INTO Lessons (Subject, Description, StartTime, EndTime, TutorId, StudentId, Location, RecurrenceRule, IsRecurring, CreatedUTC)
                VALUES (@Subject, @Description, @StartTime, @EndTime, @TutorId, @StudentId, @Location, @RecurrenceRule, @IsRecurring, GETUTCDATE());
                
                SELECT SCOPE_IDENTITY();";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);
            
            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                lesson.Subject,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.Location,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            lesson.Id = newId;
            return lesson;
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                UPDATE Lessons 
                SET Subject = @Subject,
                    Description = @Description,
                    StartTime = @StartTime,
                    EndTime = @EndTime,
                    TutorId = @TutorId,
                    StudentId = @StudentId,
                    Location = @Location,
                    RecurrenceRule = @RecurrenceRule,
                    IsRecurring = @IsRecurring,
                    UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id AND IsArchived = 0";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);
            
            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                lesson.Subject,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.Location,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            
            var sql = @"
                UPDATE Lessons 
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Tutor>> GetTutorsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE IsArchived = 0 ORDER BY TutorName";
            return await connection.QueryAsync<Tutor>(sql);
        }

        public async Task<Tutor?> GetTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Tutors WHERE TutorId = @Id AND IsArchived = 0";
            return await connection.QueryFirstOrDefaultAsync<Tutor>(sql, new { Id = id });
        }

        public async Task<Tutor> CreateTutorAsync(Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Tutors (TutorName, Email, LoginName, Password, Color, CreatedUTC, IsArchived)
                VALUES (@TutorName, @Email, @LoginName, @Password, @Color, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                tutor.TutorName,
                tutor.Email,
                tutor.LoginName,
                tutor.Password,
                tutor.Color
            });

            tutor.TutorId = newId;
            return tutor;
        }

        public async Task<bool> UpdateTutorAsync(int id, Tutor tutor)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET TutorName = @TutorName,
                    Email = @Email,
                    LoginName = @LoginName,
                    Password = @Password,
                    Color = @Color,
                    UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                tutor.TutorName,
                tutor.Email,
                tutor.LoginName,
                tutor.Password,
                tutor.Color
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTutorAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Student>> GetStudentsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM Students WHERE IsArchived = 0";
            return await connection.QueryAsync<Student>(sql);
        }

        public async Task<bool> UpdateTutorColorAsync(int tutorId, string color)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Tutors
                SET Color = @Color, UpdatedUTC = GETUTCDATE()
                WHERE TutorId = @TutorId AND IsArchived = 0";

            var parameters = new
            {
                TutorId = tutorId,
                Color = color
            };

            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }
    }
}
