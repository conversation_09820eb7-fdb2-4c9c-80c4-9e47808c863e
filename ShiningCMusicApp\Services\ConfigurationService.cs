using System.Net.Http.Json;

namespace ShiningCMusicApp.Services
{
    public interface IConfigurationService
    {
        Task<string> GetApiBaseUrlAsync();
        Task<string?> GetSyncfusionLicenseAsync();
    }

    public class ConfigurationService : IConfigurationService
    {
        private readonly HttpClient _httpClient;
        private Dictionary<string, string>? _config;
        private readonly SemaphoreSlim _semaphore = new(1, 1);

        public ConfigurationService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        private async Task LoadConfigurationAsync()
        {
            if (_config != null) return;

            await _semaphore.WaitAsync();
            try
            {
                if (_config != null) return; // Double-check after acquiring lock

                try
                {
                    _config = await _httpClient.GetFromJsonAsync<Dictionary<string, string>>("appsettings.json");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to load configuration: {ex.Message}");
                    _config = new Dictionary<string, string>();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task<string> GetApiBaseUrlAsync()
        {
            await LoadConfigurationAsync();
            
            if (_config != null && _config.TryGetValue("ApiBaseUrl", out var baseUrl))
            {
                return baseUrl;
            }
            
            // Fallback to default
            return "https://localhost:7268/api";
        }

        public async Task<string?> GetSyncfusionLicenseAsync()
        {
            await LoadConfigurationAsync();
            
            if (_config != null && _config.TryGetValue("SyncfusionLicense", out var license))
            {
                return license;
            }
            
            return null;
        }
    }
}
