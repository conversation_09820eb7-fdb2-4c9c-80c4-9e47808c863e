using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TutorsController : ControllerBase
    {
        private readonly ILessonService _lessonService;
        private readonly ILogger<TutorsController> _logger;

        public TutorsController(ILessonService lessonService, ILogger<TutorsController> logger)
        {
            _lessonService = lessonService;
            _logger = logger;
        }

        // GET: api/tutors
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetTutors()
        {
            try
            {
                var tutors = await _lessonService.GetTutorsAsync();
                return Ok(tutors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutors");
                return StatusCode(500, "Internal server error");
            }
        }

        // PATCH: api/tutors/{id}/color
        [HttpPatch("{id}/color")]
        public async Task<IActionResult> UpdateTutorColor(int id, [FromBody] UpdateTutorColorRequest request)
        {
            try
            {
                var success = await _lessonService.UpdateTutorColorAsync(id, request.Color);
                if (success)
                {
                    return Ok(new { message = "Tutor color updated successfully" });
                }
                return NotFound(new { message = "Tutor not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor color");
                return StatusCode(500, new { message = ex.Message });
            }
        }
    }

    public class UpdateTutorColorRequest
    {
        public string Color { get; set; } = string.Empty;
    }
}
