using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Student
    {
        public int StudentId { get; set; }
        
        [StringLength(50)]
        public string? StudentName { get; set; }
        
        [StringLength(250)]
        public string? Email { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }
        
        public bool IsArchived { get; set; } = false;
        
        public int? TutorID { get; set; }
        
        // Navigation properties
        public virtual Tutor? Tutor { get; set; }
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();
    }
}
