using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Lesson
    {
        public int LessonId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [Required]
        public DateTime StartTime { get; set; }
        
        [Required]
        public DateTime EndTime { get; set; }
        
        [Required]
        public int TutorId { get; set; }
        
        [Required]
        public int StudentId { get; set; }
        
        [StringLength(100)]
        public string? Location { get; set; }
        
        public bool IsRecurring { get; set; } = false;
        
        [StringLength(500)]
        public string? RecurrenceRule { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }
        
        public bool IsArchived { get; set; } = false;
        
        // Navigation properties
        public virtual Tutor? Tutor { get; set; }
        public virtual Student? Student { get; set; }
    }
}
