using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StudentsController : ControllerBase
    {
        private readonly ILessonService _lessonService;
        private readonly ILogger<StudentsController> _logger;

        public StudentsController(ILessonService lessonService, ILogger<StudentsController> logger)
        {
            _lessonService = lessonService;
            _logger = logger;
        }

        // GET: api/students
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Student>>> GetStudents()
        {
            try
            {
                var students = await _lessonService.GetStudentsAsync();
                return Ok(students);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving students");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/students/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Student>> GetStudent(int id)
        {
            try
            {
                var student = await _lessonService.GetStudentAsync(id);
                if (student == null)
                {
                    return NotFound(new { message = "Student not found" });
                }
                return Ok(student);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving student");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/students
        [HttpPost]
        public async Task<ActionResult<Student>> CreateStudent([FromBody] Student student)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(student.StudentName))
                {
                    return BadRequest(new { message = "Student name is required" });
                }

                var createdStudent = await _lessonService.CreateStudentAsync(student);
                return CreatedAtAction(nameof(GetStudent), new { id = createdStudent.StudentId }, createdStudent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating student");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/students/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStudent(int id, [FromBody] Student student)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(student.StudentName))
                {
                    return BadRequest(new { message = "Student name is required" });
                }

                var success = await _lessonService.UpdateStudentAsync(id, student);
                if (success)
                {
                    return Ok(new { message = "Student updated successfully" });
                }
                return NotFound(new { message = "Student not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating student");
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/students/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStudent(int id)
        {
            try
            {
                var success = await _lessonService.DeleteStudentAsync(id);
                if (success)
                {
                    return Ok(new { message = "Student deleted successfully" });
                }
                return NotFound(new { message = "Student not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting student");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
