using IdentityServer4.Models;
using IdentityServer4.Stores;
using Microsoft.EntityFrameworkCore;
using ShiningCMusicApi.Infrastructure;
using ShiningCMusicApi.Services;

var builder = WebApplication.CreateBuilder(args);

// Add logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

// Add services to the container.

// Add connection string
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// Add our services
builder.Services.AddScoped<ILessonService, LessonService>();

// Add IdentityServer4
//builder.Services.AddIdentityServer()
//    .AddDeveloperSigningCredential()
//    .AddInMemoryClients(GetClients())
//    .AddInMemoryApiScopes(GetApiScopes())
//    .AddInMemoryApiResources(GetApiResources());

// Add DbContext with connection string
builder.Services.AddDbContext<ClientDbContext>(options =>
    options.UseSqlServer(Environment.GetEnvironmentVariable("ConnectionStrings_DefaultConnection") ?? builder.Configuration.GetConnectionString("DefaultConnection")));

// Register the custom client store
builder.Services.AddScoped<IClientStore, DatabaseClientStore>();

// Register AuthConfig as a scoped service
builder.Services.AddScoped<AuthConfig>(provider =>
    new AuthConfig(provider.GetRequiredService<IConfiguration>()));

var authConfig = new AuthConfig(builder.Configuration);
builder.Services.AddIdentityServer()
    .AddInMemoryApiResources(authConfig.ApiResources) // Directly pass ApiResources
    .AddInMemoryApiScopes(authConfig.ApiScopes)       // Directly pass ApiScopes
    .AddClientStore<DatabaseClientStore>()
    .AddDeveloperSigningCredential();

var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL") ?? builder.Configuration["ApiBaseUrl"];

// Add Authentication
builder.Services.AddAuthentication("Bearer")
    .AddIdentityServerAuthentication("Bearer", options =>
    {
        options.Authority = apiBaseUrl;
        options.RequireHttpsMetadata = false;
        options.ApiName = "ShiningCMusicApi";
    });

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();
app.UseCors("AllowBlazorApp");
app.UseIdentityServer();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
