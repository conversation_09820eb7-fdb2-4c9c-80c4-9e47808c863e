using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using ShiningCMusicApp;
using ShiningCMusicApp.Services;
using Syncfusion.Blazor;

// Register Syncfusion license (Community License - Free)
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9");

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Add our services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<ILessonApiService, LessonApiService>();

// Add Syncfusion Blazor service
builder.Services.AddSyncfusionBlazor();

await builder.Build().RunAsync();
