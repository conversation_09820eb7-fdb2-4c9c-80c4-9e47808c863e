@page "/tutors"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime

<PageTitle>Tutor Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🎓 Tutor Management</h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading tutors...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Tutors</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" @onclick="OpenCreateModal">
                                <i class="fas fa-plus"></i> Add New Tutor
                            </button>
                            <button class="btn btn-secondary" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfGrid DataSource="@tutors" AllowPaging="true" AllowSorting="true" AllowFiltering="true" 
                                AllowResizing="true" Height="600">
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Tutor.TutorId) HeaderText="ID" Width="80" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.TutorName) HeaderText="Name" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Email) HeaderText="Email" Width="250"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.LoginName) HeaderText="Login Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Color) HeaderText="Color" Width="120">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <div class="d-flex align-items-center">
                                            <div style="width: 20px; height: 20px; background-color: @tutor?.Color; border: 1px solid #ccc; margin-right: 8px;"></div>
                                            <span>@tutor?.Color</span>
                                        </div>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Tutor.CreatedUTC) HeaderText="Created" Width="150" Format="d" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <div class="btn-group w-100" role="group">
                                            <button class="btn btn-outline-primary flex-fill" @onclick="() => OpenEditModal(tutor)"
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger flex-fill" @onclick="() => DeleteTutor(tutor)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTutor" OnValidSubmit="@SaveTutor">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Tutor Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentTutor.TutorName" Placeholder="Enter tutor name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.TutorName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentTutor.Email" Placeholder="Enter email address" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.Email)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentTutor.LoginName" Placeholder="Enter login name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.LoginName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <SfTextBox @bind-Value="currentTutor.Password" Type="InputType.Password" 
                               Placeholder="Enter password" CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.Password)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Color</label>
                    <div class="d-flex align-items-center">
                        <input type="color" @bind="currentTutor.Color" class="form-control form-control-color me-2" 
                               style="width: 60px;" />
                        <SfTextBox @bind-Value="currentTutor.Color" Placeholder="#6C757D" 
                                   CssClass="form-control"></SfTextBox>
                    </div>
                    <ValidationMessage For="@(() => currentTutor.Color)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-secondary" @onclick="CloseModal">Cancel</SfButton>
                    <SfButton CssClass="btn btn-primary" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditMode ? "Update" : "Create")
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<Tutor> tutors = new();
    private bool isLoading = true;
    private bool showModal = false;
    private bool isEditMode = false;
    private bool isSaving = false;
    private string modalTitle = "";
    private Tutor currentTutor = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadTutors();
    }

    private async Task LoadTutors()
    {
        isLoading = true;
        try
        {
            tutors = await LessonApi.GetTutorsAsync();
            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {tutors.Count} tutors");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading tutors: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading tutors: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadTutors();
    }

    private void OpenCreateModal()
    {
        currentTutor = new Tutor { Color = "#6C757D" };
        isEditMode = false;
        modalTitle = "Create New Tutor";
        showModal = true;
    }

    private void OpenEditModal(Tutor? tutor)
    {
        if (tutor != null)
        {
            currentTutor = new Tutor
            {
                TutorId = tutor.TutorId,
                TutorName = tutor.TutorName,
                Email = tutor.Email,
                LoginName = tutor.LoginName,
                Password = tutor.Password,
                Color = tutor.Color ?? "#6C757D"
            };
            isEditMode = true;
            modalTitle = "Edit Tutor";
            showModal = true;
        }
    }

    private void CloseModal()
    {
        showModal = false;
        currentTutor = new();
        isSaving = false;
    }

    private async Task SaveTutor()
    {
        if (string.IsNullOrWhiteSpace(currentTutor.TutorName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Tutor name is required.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await LessonApi.UpdateTutorAsync(currentTutor.TutorId, currentTutor);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Tutor updated successfully!");
                }
            }
            else
            {
                var createdTutor = await LessonApi.CreateTutorAsync(currentTutor);
                success = createdTutor != null;
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Tutor created successfully!");
                }
            }

            if (success)
            {
                CloseModal();
                await LoadTutors();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save tutor. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving tutor: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving tutor: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteTutor(Tutor? tutor)
    {
        if (tutor == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to delete tutor '{tutor.TutorName}'? This action cannot be undone.");
        
        if (confirmed)
        {
            try
            {
                var success = await LessonApi.DeleteTutorAsync(tutor.TutorId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Tutor deleted successfully!");
                    await LoadTutors();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete tutor. Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting tutor: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting tutor: {ex.Message}");
            }
        }
    }
}
